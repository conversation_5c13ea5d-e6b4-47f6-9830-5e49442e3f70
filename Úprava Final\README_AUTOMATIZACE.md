# Automatizace nejnovějších videí - Funmaggedon Website

## Co bylo změněno

Tato verze obsahuje automatizaci pro načítání nejnovějších videí ze sociálních sítí. Automatizace funguje na dvou stránkách:

1. **Homepage (index.html)** - Automatické načítání nejnovějšího YouTube videa
2. **Gallery (gallery.html)** - Automatizace pro všechny platformy (YouTube, TikTok, Instagram, Facebook)

### Změněné soubory:

1. **index.html** - Upravená homepage s automatickým načítáním nejnovějšího videa
2. **gallery.html** - Upravená galerie pro automatické načítání
3. **js/video-loader.js** - Nový JavaScript pro automatizaci (NOVÝ SOUBOR)
4. **css/style.css** - Přidané styly pro loading placeholders

## Jak automatizace funguje

### Homepage (index.html)
- **YouTube automatizace**: Na<PERSON><PERSON>t<PERSON> nejn<PERSON>ěj<PERSON> video z YouTube kanálu
- **Inteligentní detekce**: Script automaticky detekuje, že je na homepage
- **Fallback systém**: V případě selhání API se zobrazí přednastavené video

### Gallery (gallery.html)
- **YouTube**: Skutečné automatické načítání nejnovějších videí přes RSS feed
- **TikTok**: Elegantní placeholder s přímým odkazem na profil
- **Instagram**: Stylový placeholder s přesměrováním na Instagram
- **Facebook**: Profesionální placeholder s odkazem na Facebook stránku

### Technické detaily
- Používá CORS proxy servery pro obejití omezení
- V případě selhání používá fallback video (aktuálně nastaveno na 'MvpAGjRbfEM')
- Automatická detekce stránky podle přítomnosti HTML elementů

## Konfigurace

### Změna YouTube kanálu
V souboru `js/video-loader.js` na řádku 7:
```javascript
youtube: '@funmaggedon', // Změňte na váš YouTube handle
```

### Změna fallback videa
V souboru `js/video-loader.js` na řádku 15:
```javascript
youtube: 'MvpAGjRbfEM', // Změňte na ID vašeho fallback videa
```

### Změna názvů profilů
V souboru `js/video-loader.js` na řádcích 8-10:
```javascript
tiktok: '@funmaggedon',
instagram: 'funmaggedon',
facebook: 'funmaggedon'
```

## Výhody automatizace

1. **YouTube**: Skutečně automatické načítání nejnovějších videí
2. **Ostatní platformy**: Elegantní řešení s přesměrováním na profily
3. **Fallback systém**: Vždy se zobrazí nějaký obsah, i když API selže
4. **Loading animace**: Uživatelé vidí, že se obsah načítá
5. **Responzivní design**: Funguje na všech zařízeních

## Omezení a poznámky

### YouTube API
- RSS feed může být občas nedostupný
- CORS proxy servery mohou mít omezení
- Pro plnou funkcionalnost doporučujeme získat YouTube Data API klíč

### Ostatní platformy
- TikTok, Instagram a Facebook vyžadují autentifikaci pro API
- Současné řešení s placeholdery je uživatelsky přívětivé
- Pro skutečnou automatizaci by bylo potřeba získat API klíče

## Instalace YouTube Data API (volitelné)

Pro lepší spolehlivost můžete implementovat YouTube Data API v3:

1. Získejte API klíč na [Google Cloud Console](https://console.cloud.google.com/)
2. Nahraďte RSS metodu v `video-loader.js`:

```javascript
const API_KEY = 'VÁŠ_API_KLÍČ';
const CHANNEL_ID = 'VÁŠ_CHANNEL_ID';
const url = `https://www.googleapis.com/youtube/v3/search?key=${API_KEY}&channelId=${CHANNEL_ID}&part=snippet,id&order=date&maxResults=1`;
```

## Testování

1. Otevřete `gallery.html` v prohlížeči
2. Zkontrolujte Developer Console (F12) pro logy
3. Ověřte, že se načítají placeholdery a poté obsah
4. Otestujte na různých zařízeních

## Podpora

Automatizace je navržena tak, aby byla robustní a spolehlivá. V případě problémů:

1. Zkontrolujte internetové připojení
2. Ověřte, že jsou správně nastavené názvy kanálů
3. Zkontrolujte Developer Console pro chybové zprávy

## Budoucí vylepšení

- Implementace YouTube Data API pro větší spolehlivost
- Přidání cache mechanismu pro rychlejší načítání
- Možnost nastavení refresh intervalu
- Integrace s dalšími video platformami
