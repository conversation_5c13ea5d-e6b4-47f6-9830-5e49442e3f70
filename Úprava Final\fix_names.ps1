# PowerShell skript pro opravu názvů Funmageddon
# Opravuje všechny výskyty nesprávných názvů na správný "Funmageddon"

Write-Host "Spouštím opravu názvů Funmageddon..." -ForegroundColor Green

# Seznam souborů k opravě
$files = @(
    "index.html",
    "about.html", 
    "characters.html",
    "contact.html",
    "gallery.html",
    "js\script.js",
    "js\video-loader.js",
    "README_AUTOMATIZACE.md",
    "demo_automatizace.html"
)

# Definice náhrad
$replacements = @(
    @("funmaggedon", "funmageddon"),
    @("Funmaggedon", "Funmageddon"),
    @("FUNMAGGEDON", "FUNMAGEDDON"),
    @("funmaggeddon", "funmageddon"),
    @("Funmaggeddon", "Funmageddon"),
    @("FUNMAGGEDDON", "FUNMAGEDDON"),
    @("funmaggeddonu", "funmageddonu"),
    @("Funmaggeddonu", "Funmageddonu")
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "Opravuji soubor: $file" -ForegroundColor Yellow
        
        $content = Get-Content $file -Raw -Encoding UTF8
        $originalContent = $content
        
        foreach ($replacement in $replacements) {
            $old = $replacement[0]
            $new = $replacement[1]
            $content = $content -replace [regex]::Escape($old), $new
        }
        
        if ($content -ne $originalContent) {
            Set-Content $file -Value $content -Encoding UTF8 -NoNewline
            Write-Host "  ✓ Opraveno" -ForegroundColor Green
        } else {
            Write-Host "  - Žádné změny" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ✗ Soubor nenalezen: $file" -ForegroundColor Red
    }
}

Write-Host "`nOprava dokončena!" -ForegroundColor Green
Write-Host "Všechny výskyty 'funmaggedon' a 'funmaggeddon' byly opraveny na 'funmageddon'." -ForegroundColor Cyan
