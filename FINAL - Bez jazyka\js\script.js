﻿// Simplified script for English-only version - no translations needed

// DOM elements
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

// Mobile navigation toggle
if (hamburger && navMenu) {
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    }));
}

// Copy email functionality for contact page
function copyEmail() {
    const email = '<EMAIL>';
    navigator.clipboard.writeText(email).then(() => {
        const copyMsg = document.getElementById('copy-msg');
        if (copyMsg) {
            copyMsg.textContent = 'Copied!';
            copyMsg.style.opacity = '1';
            setTimeout(() => {
                copyMsg.style.opacity = '0';
            }, 2000);
        }
    }).catch(err => {
        console.error('Failed to copy email: ', err);
        // Fallback for older browsers
        const copyMsg = document.getElementById('copy-msg');
        if (copyMsg) {
            copyMsg.textContent = 'Copy failed - please select manually';
            copyMsg.style.opacity = '1';
            setTimeout(() => {
                copyMsg.style.opacity = '0';
            }, 3000);
        }
    });
}

// Initialize copy message as hidden when page loads
document.addEventListener('DOMContentLoaded', () => {
    const copyMsg = document.getElementById('copy-msg');
    if (copyMsg) {
        copyMsg.style.opacity = '0';
    }
});

// Make copyEmail function globally available
window.copyEmail = copyEmail;