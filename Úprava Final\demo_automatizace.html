<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo automatizace videí - Funma<PERSON>on</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .demo-title {
            color: #333;
            border-bottom: 2px solid #FFD700;
            padding-bottom: 10px;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        .before {
            background: #ffebee;
            border-left: 4px solid #f44336;
        }
        .after {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .code-block {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 10px 0;
        }
        .highlight {
            background: #FFD700;
            padding: 2px 4px;
            border-radius: 3px;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #4caf50;
            font-weight: bold;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        @media (max-width: 768px) {
            .comparison {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>🎬 Demo automatizace videí - Funmaggedon Gallery</h1>
    
    <div class="demo-section">
        <h2 class="demo-title">📋 Přehled změn</h2>
        <p>Tato automatizace řeší problém <span class="highlight">manuálního aktualizování nejnovějších videí</span> na webu. Automatizace nyní funguje na <strong>dvou stránkách</strong>:</p>

        <div class="info">
            <h3>🏠 Homepage (index.html)</h3>
            <p>Automatické načítání nejnovějšího YouTube videa v sekci "Nejnovější video"</p>
        </div>

        <div class="info">
            <h3>🎬 Gallery (gallery.html)</h3>
            <p>Kompletní automatizace pro všechny platformy - YouTube, TikTok, Instagram, Facebook</p>
        </div>

        <div class="comparison">
            <div class="before">
                <h3>❌ Před automatizací</h3>
                <ul>
                    <li>Ruční změna HTML kódu při každém novém videu</li>
                    <li>Riziko zapomnění aktualizace na obou stránkách</li>
                    <li>Statické odkazy na konkrétní videa</li>
                    <li>Časově náročná údržba</li>
                    <li>Nekonzistentní obsah mezi stránkami</li>
                </ul>
            </div>
            <div class="after">
                <h3>✅ Po automatizaci</h3>
                <ul>
                    <li>Automatické načítání nejnovějších videí</li>
                    <li>Vždy aktuální obsah bez zásahu</li>
                    <li>Dynamické načítání přes API</li>
                    <li>Minimální údržba</li>
                    <li>Konzistentní obsah na všech stránkách</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">🔧 Technické řešení</h2>
        
        <h3>YouTube automatizace</h3>
        <div class="code-block">
// Původní statický kód:
&lt;iframe src="https://www.youtube.com/embed/MvpAGjRbfEM"&gt;&lt;/iframe&gt;

// Nový automatický kód:
&lt;div id="youtube-latest"&gt;
    &lt;div class="loading-placeholder"&gt;Načítám nejnovější video...&lt;/div&gt;
&lt;/div&gt;
        </div>

        <h3>Homepage automatizace</h3>
        <div class="code-block">
// Původní statický kód na homepage:
&lt;iframe src="https://www.youtube.com/embed/MvpAGjRbfEM"&gt;&lt;/iframe&gt;

// Nový automatický kód:
&lt;div id="homepage-latest-video"&gt;
    &lt;div class="loading-placeholder"&gt;Načítám nejnovější video...&lt;/div&gt;
&lt;/div&gt;
        </div>

        <h3>JavaScript automatizace</h3>
        <div class="code-block">
class VideoLoader {
    async init() {
        // Inteligentní detekce stránky
        const isHomepage = document.getElementById('homepage-latest-video') !== null;
        const isGalleryPage = document.getElementById('youtube-latest') !== null;

        if (isHomepage) {
            await this.loadHomepageLatestVideo();
        }

        if (isGalleryPage) {
            await this.loadAllGalleryVideos();
        }
    }
}
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">🎯 Funkce automatizace</h2>
        
        <ul class="feature-list">
            <li><strong>Homepage YouTube:</strong> Automatické načítání nejnovějšího videa na hlavní stránce</li>
            <li><strong>Gallery YouTube:</strong> Skutečné automatické načítání nejnovějších videí přes RSS feed</li>
            <li><strong>TikTok:</strong> Elegantní placeholder s přímým odkazem na profil</li>
            <li><strong>Instagram:</strong> Stylový placeholder s přesměrováním na Instagram</li>
            <li><strong>Facebook:</strong> Profesionální placeholder s odkazem na Facebook stránku</li>
            <li><strong>Inteligentní detekce:</strong> Script automaticky rozpozná, na které stránce se nachází</li>
            <li><strong>Fallback systém:</strong> Vždy se zobrazí nějaký obsah, i když API selže</li>
            <li><strong>Loading animace:</strong> Uživatelé vidí, že se obsah načítá</li>
            <li><strong>Responzivní design:</strong> Funguje na všech zařízeních</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">⚙️ Konfigurace</h2>
        
        <h3>Změna YouTube kanálu</h3>
        <div class="code-block">
// V souboru js/video-loader.js, řádek 7:
this.channelIds = {
    youtube: '@funmaggedon', // ← Změňte na váš kanál
    tiktok: '@funmaggedon',
    instagram: 'funmaggedon',
    facebook: 'funmaggedon'
};
        </div>

        <h3>Změna fallback videa</h3>
        <div class="code-block">
// V souboru js/video-loader.js, řádek 15:
this.fallbackVideos = {
    youtube: 'MvpAGjRbfEM', // ← Změňte na ID vašeho fallback videa
    tiktok: null,
    instagram: null,
    facebook: null
};
        </div>
    </div>

    <div class="warning">
        <h3>⚠️ Důležité poznámky</h3>
        <ul>
            <li><strong>YouTube RSS feed</strong> může být občas nedostupný - proto je implementován fallback systém</li>
            <li><strong>CORS proxy servery</strong> mohou mít omezení rychlosti nebo dostupnosti</li>
            <li><strong>TikTok, Instagram, Facebook</strong> vyžadují autentifikaci pro API - současné řešení používá placeholdery</li>
        </ul>
    </div>

    <div class="info">
        <h3>💡 Doporučení pro produkci</h3>
        <ul>
            <li>Pro větší spolehlivost YouTube získejte <strong>YouTube Data API klíč</strong></li>
            <li>Implementujte <strong>cache mechanismus</strong> pro rychlejší načítání</li>
            <li>Přidejte <strong>error handling</strong> pro lepší uživatelskou zkušenost</li>
            <li>Zvažte <strong>server-side řešení</strong> pro kritické aplikace</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">🚀 Jak spustit</h2>
        <ol>
            <li><strong>Homepage test:</strong> Otevřete <code>index.html</code> v prohlížeči</li>
            <li><strong>Gallery test:</strong> Otevřete <code>gallery.html</code> v prohlížeči</li>
            <li>Zkontrolujte Developer Console (F12) pro logy načítání</li>
            <li>Ověřte, že se zobrazují loading placeholdery</li>
            <li>Po načtení by se mělo zobrazit nejnovější YouTube video nebo fallback</li>
            <li>Pro ostatní platformy (pouze gallery) se zobrazí stylové placeholdery s odkazy</li>
        </ol>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">📁 Struktura souborů</h2>
        <div class="code-block">
Úprava Final/
├── index.html                ← Upravená homepage s automatizací
├── gallery.html              ← Upravená galerie s automatizací
├── js/
│   ├── script.js             ← Původní JavaScript
│   └── video-loader.js       ← NOVÝ: Automatizace videí
├── css/
│   └── style.css             ← Rozšířené o loading styly
├── img/                      ← Obrázky (nezměněno)
├── README_AUTOMATIZACE.md    ← Dokumentace
└── demo_automatizace.html    ← Tento demo soubor
        </div>
    </div>

    <div class="demo-section">
        <h2 class="demo-title">🎉 Výsledek</h2>
        <p>Nyní máte plně funkční automatizovaný web s videi, který:</p>
        <ul class="feature-list">
            <li>Automaticky načítá nejnovější YouTube videa na homepage i v galerii</li>
            <li>Poskytuje elegantní řešení pro ostatní platformy v galerii</li>
            <li>Má robustní fallback systém</li>
            <li>Je snadno konfigurovatelný</li>
            <li>Funguje na všech zařízeních</li>
            <li>Inteligentně detekuje, na které stránce se nachází</li>
        </ul>

        <p><strong>Sekce "DOPORUČUJEME" v galerii zůstává nezměněna</strong> - můžete si tam nadále ručně nastavovat vaše nejlepší videa!</p>
    </div>

    <footer style="text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid #ddd;">
        <p>🎬 <strong>Funmaggedon Gallery Automatizace</strong> - Vytvořeno pro snadnou správu videí</p>
    </footer>
</body>
</html>
