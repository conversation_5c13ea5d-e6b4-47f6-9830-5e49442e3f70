<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kontakt - Funmaggedon</title>
    <meta name="description" content="Pro obecné dotazy a marketingovou spolupráci nás prosím kontaktujte na níže uvedený email. Preferujeme angličtinu pro komunikaci.">
    <meta name="keywords" content="Funmaggedon kontakt, marketingová spolupráce, obchodní příležitosti, email">
    <link rel="stylesheet" href="css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Nunito:wght@400;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <img src="img/funmageddon_logo.png" alt="Funmageddon Logo" class="logo">
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link" data-translate="nav-home">Domů</a>
                    </li>
                    <li class="nav-item">
                        <a href="about.html" class="nav-link" data-translate="nav-about">Co je Funmaggedon?</a>
                    </li>
                    <li class="nav-item">
                        <a href="characters.html" class="nav-link" data-translate="nav-characters">Postavy a lore</a>
                    </li>
                    <li class="nav-item">
                        <a href="gallery.html" class="nav-link" data-translate="nav-gallery">Galerie videí</a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link active" data-translate="nav-contact">Kontakt</a>
                    </li>
                </ul>
                <div class="social-icons">
                    <a href="https://youtube.com/@funmaggedon" target="_blank" class="social-link youtube">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                        </svg>
                    </a>
                    <a href="https://tiktok.com/@funmaggedon" target="_blank" class="social-link tiktok">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                        </svg>
                    </a>
                    <a href="https://instagram.com/funmaggedon" target="_blank" class="social-link instagram">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                    </a>
                    <a href="https://facebook.com/funmaggedon" target="_blank" class="social-link facebook">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                    </a>
                </div>
                <div class="language-selector">
                    <select id="language-select">
                        <option value="en">English</option>
                        <option value="cs">Čeština</option>
                        <option value="es">Español</option>
                        <option value="zh">中文</option>
                        <option value="ja">日本語</option>
                        <option value="ko">한국어</option>
                    </select>
                </div>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <main>
        <section class="page-header">
            <div class="container">
                <h1 data-translate="contact-title">Kontakt</h1>
            </div>
        </section>

        <section class="page-content">
            <div class="container">
                <div class="contact-info">
                    <p data-translate="contact-content">
                        Máte otázky, nápady na společný projekt nebo marketingovou spolupráci? Ozvěte se nám v angličtině nebo češtině na email níže.
                    </p>

                    <div style="margin: 3rem 0;">
                        <h3 data-translate="contact-email-text">Kontaktní email:</h3>
                        <div class="email-copy-container">
                            <a href="mailto:<EMAIL>" class="contact-email" data-translate="contact-email">
                                <EMAIL>
                            </a>
                            <button class="copy-btn" onclick="copyEmail()" title="Zkopírovat e-mail">
                                📋
                            </button>
                            <span id="copy-msg" class="copy-msg" data-translate="copied-message">Zkopírováno!</span>
                        </div>
                    </div>

                    
                    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 2px solid var(--accent-yellow);">
                        <p style="font-size: 0.9rem; color: var(--text-gray);" data-translate="contact-response-time">
                            Odpovídáme do 48 hodin.
                        </p>
                        <p style="font-size: 1rem; color: var(--text-primary); margin-top: 1rem;" data-translate="contact-closing">
                            Těšíme se na vaši zprávu a na to, jak společně posuneme hranice zábavy!
                        </p>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="img/funmaggedon_logo.png" alt="Funmaggedon Logo">
                </div>
                <div class="footer-links">
                    <a href="about.html" data-translate="nav-about">Co je Funmaggedon?</a>
                    <a href="characters.html" data-translate="nav-characters">Postavy a lore</a>
                    <a href="gallery.html" data-translate="nav-gallery">Galerie videí</a>
                    <a href="contact.html" data-translate="nav-contact">Kontakt</a>
                </div>
                <div class="footer-social">
                    <a href="https://youtube.com/@funmaggedon" target="_blank" class="social-link youtube">YouTube</a>
                    <a href="https://tiktok.com/@funmaggedon" target="_blank" class="social-link tiktok">TikTok</a>
                    <a href="https://instagram.com/funmaggedon" target="_blank" class="social-link instagram">Instagram</a>
                    <a href="https://facebook.com/funmaggedon" target="_blank" class="social-link facebook">Facebook</a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Funmaggedon. <span data-translate="footer-rights">Všechna práva vyhrazena.</span></p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
function copyEmail() {
    const email = "<EMAIL>";
    navigator.clipboard.writeText(email).then(() => {
        const msg = document.getElementById("copy-msg");
        if (msg) {
            msg.style.opacity = "1";
            setTimeout(() => {
                msg.style.opacity = "0";
            }, 2000);
        }
    }).catch(err => {
        console.error('Failed to copy email: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = email;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        const msg = document.getElementById("copy-msg");
        if (msg) {
            msg.style.opacity = "1";
            setTimeout(() => {
                msg.style.opacity = "0";
            }, 2000);
        }
    });
}
</script>
</body>
</html>

