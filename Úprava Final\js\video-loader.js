// <PERSON><PERSON>é <PERSON><PERSON><PERSON><PERSON>ch videí z různých platforem
// Video Loader for Funmageddon Gallery

class VideoLoader {
    constructor() {
        this.channelIds = {
            youtube: '@funmageddon', // YouTube handle
            tiktok: '@funmageddon',
            instagram: 'funmageddon',
            facebook: 'funmageddon'
        };
        
        // Fallback videa pro případ, že API selže
        this.fallbackVideos = {
            youtube: 'MvpAGjRbfEM',
            tiktok: null,
            instagram: null,
            facebook: null
        };
    }

    // Načtení nejnovějšího YouTube videa pomocí různých metod
    async loadLatestYouTubeVideo() {
        try {
            const container = document.getElementById('youtube-latest');
            if (!container) return;

            // Metoda 1: Pokus o RSS feed s CORS proxy
            try {
                const proxyUrl = 'https://api.allorigins.win/get?url=';
                const channelName = this.channelIds.youtube.replace('@', '');
                const targetUrl = encodeURIComponent(`https://www.youtube.com/feeds/videos.xml?user=${channelName}`);
                
                const response = await fetch(proxyUrl + targetUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.contents) {
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(data.contents, 'text/xml');
                        const entries = xmlDoc.getElementsByTagName('entry');
                        
                        if (entries.length > 0) {
                            const latestEntry = entries[0];
                            const videoId = latestEntry.getElementsByTagName('yt:videoId')[0]?.textContent;
                            
                            if (videoId) {
                                console.log('Načteno nejnovější YouTube video:', videoId);
                                this.renderYouTubeVideo(container, videoId);
                                return;
                            }
                        }
                    }
                }
            } catch (error) {
                console.log('RSS feed nedostupný:', error.message);
            }

            // Metoda 2: Pokus o alternativní RSS endpoint
            try {
                const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
                const channelName = this.channelIds.youtube.replace('@', '');
                const targetUrl = `https://www.youtube.com/feeds/videos.xml?user=${channelName}`;
                
                const response = await fetch(proxyUrl + targetUrl);
                if (response.ok) {
                    const xmlText = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
                    const entries = xmlDoc.getElementsByTagName('entry');
                    
                    if (entries.length > 0) {
                        const latestEntry = entries[0];
                        const videoId = latestEntry.getElementsByTagName('yt:videoId')[0]?.textContent;
                        
                        if (videoId) {
                            console.log('Načteno nejnovější YouTube video (metoda 2):', videoId);
                            this.renderYouTubeVideo(container, videoId);
                            return;
                        }
                    }
                }
            } catch (error) {
                console.log('Alternativní RSS feed nedostupný:', error.message);
            }
            
            // Fallback na přednastavené video
            console.log('Používám fallback YouTube video');
            this.renderYouTubeVideo(container, this.fallbackVideos.youtube);
            
        } catch (error) {
            console.error('Chyba při načítání YouTube videa:', error);
            this.showError('youtube-latest', 'Nepodařilo se načíst nejnovější YouTube video');
        }
    }

    // Vykreslení YouTube videa
    renderYouTubeVideo(container, videoId) {
        container.innerHTML = `
            <iframe
                src="https://www.youtube.com/embed/${videoId}"
                title="Nejnovější Funmageddon YouTube video"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowfullscreen>
            </iframe>
        `;
    }

    // Načtení nejnovějšího YouTube videa pro homepage
    async loadHomepageLatestVideo() {
        try {
            const container = document.getElementById('homepage-latest-video');
            if (!container) return;

            // Použijeme stejnou logiku jako pro gallery, ale s jiným kontejnerem
            // Metoda 1: Pokus o RSS feed s CORS proxy
            try {
                const proxyUrl = 'https://api.allorigins.win/get?url=';
                const channelName = this.channelIds.youtube.replace('@', '');
                const targetUrl = encodeURIComponent(`https://www.youtube.com/feeds/videos.xml?user=${channelName}`);

                const response = await fetch(proxyUrl + targetUrl, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.contents) {
                        const parser = new DOMParser();
                        const xmlDoc = parser.parseFromString(data.contents, 'text/xml');
                        const entries = xmlDoc.getElementsByTagName('entry');

                        if (entries.length > 0) {
                            const latestEntry = entries[0];
                            const videoId = latestEntry.getElementsByTagName('yt:videoId')[0]?.textContent;

                            if (videoId) {
                                console.log('Načteno nejnovější YouTube video pro homepage:', videoId);
                                this.renderYouTubeVideo(container, videoId);
                                return;
                            }
                        }
                    }
                }
            } catch (error) {
                console.log('RSS feed nedostupný pro homepage:', error.message);
            }

            // Metoda 2: Pokus o alternativní RSS endpoint
            try {
                const proxyUrl = 'https://cors-anywhere.herokuapp.com/';
                const channelName = this.channelIds.youtube.replace('@', '');
                const targetUrl = `https://www.youtube.com/feeds/videos.xml?user=${channelName}`;

                const response = await fetch(proxyUrl + targetUrl);
                if (response.ok) {
                    const xmlText = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
                    const entries = xmlDoc.getElementsByTagName('entry');

                    if (entries.length > 0) {
                        const latestEntry = entries[0];
                        const videoId = latestEntry.getElementsByTagName('yt:videoId')[0]?.textContent;

                        if (videoId) {
                            console.log('Načteno nejnovější YouTube video pro homepage (metoda 2):', videoId);
                            this.renderYouTubeVideo(container, videoId);
                            return;
                        }
                    }
                }
            } catch (error) {
                console.log('Alternativní RSS feed nedostupný pro homepage:', error.message);
            }

            // Fallback na přednastavené video
            console.log('Používám fallback YouTube video pro homepage');
            this.renderYouTubeVideo(container, this.fallbackVideos.youtube);

        } catch (error) {
            console.error('Chyba při načítání YouTube videa pro homepage:', error);
            this.showError('homepage-latest-video', 'Nepodařilo se načíst nejnovější video');
        }
    }

    // Načtení nejnovějšího TikTok videa
    async loadLatestTikTokVideo() {
        try {
            const container = document.getElementById('tiktok-latest');
            if (!container) return;

            // TikTok API je omezené, použijeme placeholder s odkazem na profil
            container.innerHTML = `
                <div class="tiktok-placeholder" style="background: linear-gradient(135deg, #ff0050, #00f2ea); padding: 3rem; text-align: center; border-radius: 15px; color: white;">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">📱</div>
                    <p style="margin: 0; font-weight: bold;">Nejnovější TikTok obsah</p>
                    <p style="margin: 0.5rem 0; opacity: 0.9;">Sledujte nás na TikToku pro nejčerstvější videa!</p>
                    <a href="https://tiktok.com/${this.channelIds.tiktok}" target="_blank" 
                       style="display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); 
                              color: white; text-decoration: none; border-radius: 25px; border: 1px solid rgba(255,255,255,0.3);">
                        Otevřít TikTok profil
                    </a>
                </div>
            `;
            
        } catch (error) {
            console.error('Chyba při načítání TikTok videa:', error);
            this.showError('tiktok-latest', 'Nepodařilo se načíst nejnovější TikTok video');
        }
    }

    // Načtení nejnovějšího Instagram videa
    async loadLatestInstagramVideo() {
        try {
            const container = document.getElementById('instagram-latest');
            if (!container) return;

            // Instagram API vyžaduje autentifikaci, použijeme placeholder
            container.innerHTML = `
                <div class="instagram-placeholder" style="background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%); 
                     padding: 3rem; text-align: center; border-radius: 15px; color: white;">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">📸</div>
                    <p style="margin: 0; font-weight: bold;">Nejnovější Instagram obsah</p>
                    <p style="margin: 0.5rem 0; opacity: 0.9;">Sledujte nás na Instagramu pro nejčerstvější příspěvky!</p>
                    <a href="https://instagram.com/${this.channelIds.instagram}" target="_blank" 
                       style="display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); 
                              color: white; text-decoration: none; border-radius: 25px; border: 1px solid rgba(255,255,255,0.3);">
                        Otevřít Instagram profil
                    </a>
                </div>
            `;
            
        } catch (error) {
            console.error('Chyba při načítání Instagram videa:', error);
            this.showError('instagram-latest', 'Nepodařilo se načíst nejnovější Instagram video');
        }
    }

    // Načtení nejnovějšího Facebook videa
    async loadLatestFacebookVideo() {
        try {
            const container = document.getElementById('facebook-latest');
            if (!container) return;

            // Facebook API vyžaduje autentifikaci, použijeme placeholder
            container.innerHTML = `
                <div class="facebook-placeholder" style="background: #1877f2; padding: 3rem; text-align: center; border-radius: 15px; color: white;">
                    <div style="font-size: 2rem; margin-bottom: 1rem;">👥</div>
                    <p style="margin: 0; font-weight: bold;">Nejnovější Facebook obsah</p>
                    <p style="margin: 0.5rem 0; opacity: 0.9;">Sledujte nás na Facebooku pro nejčerstvější příspěvky!</p>
                    <a href="https://facebook.com/${this.channelIds.facebook}" target="_blank" 
                       style="display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: rgba(255,255,255,0.2); 
                              color: white; text-decoration: none; border-radius: 25px; border: 1px solid rgba(255,255,255,0.3);">
                        Otevřít Facebook stránku
                    </a>
                </div>
            `;
            
        } catch (error) {
            console.error('Chyba při načítání Facebook videa:', error);
            this.showError('facebook-latest', 'Nepodařilo se načíst nejnovější Facebook video');
        }
    }

    // Zobrazení chybové zprávy
    showError(containerId, message) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div style="background: #f8d7da; color: #721c24; padding: 2rem; text-align: center; border-radius: 15px; border: 1px solid #f5c6cb;">
                    <p style="margin: 0;">${message}</p>
                    <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem; opacity: 0.8;">Zkuste obnovit stránku později.</p>
                </div>
            `;
        }
    }

    // Inicializace načítání všech videí
    async init() {
        console.log('Inicializuji načítání nejnovějších videí...');

        // Detekce stránky podle přítomnosti elementů
        const isGalleryPage = document.getElementById('youtube-latest') !== null;
        const isHomepage = document.getElementById('homepage-latest-video') !== null;

        if (isHomepage) {
            console.log('Detekována homepage - načítám nejnovější video');
            await this.loadHomepageLatestVideo();
        }

        if (isGalleryPage) {
            console.log('Detekována gallery stránka - načítám všechna videa');
            // Načteme videa paralelně pro gallery
            await Promise.all([
                this.loadLatestYouTubeVideo(),
                this.loadLatestTikTokVideo(),
                this.loadLatestInstagramVideo(),
                this.loadLatestFacebookVideo()
            ]);
        }

        console.log('Načítání videí dokončeno');
    }
}

// Inicializace po načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    // Počkáme chvilku, aby se načetly ostatní skripty
    setTimeout(() => {
        const videoLoader = new VideoLoader();
        videoLoader.init();
    }, 1000);
});

// Export pro případné další použití
window.VideoLoader = VideoLoader;
