/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color palette inspired by logo and background */
    --primary-bg: #1a2332;
    --secondary-bg: #243447;
    --accent-yellow: #FFD700;
    --accent-orange: #FF8C00;
    --accent-blue: #4A90E2;
    --text-light: #FFFFFF;
    --text-gray: #B8C5D1;
    --text-dark: #2C3E50;
    --gradient-primary: linear-gradient(135deg, #FFD700 0%, #FF8C00 100%);
    --gradient-secondary: linear-gradient(135deg, #4A90E2 0%, #2980B9 100%);
    --shadow-light: 0 4px 15px rgba(255, 215, 0, 0.3);
    --shadow-dark: 0 8px 30px rgba(0, 0, 0, 0.4);
}

body {
    font-family: 'Nunito', sans-serif;
    background: var(--primary-bg);
    background-image: url('../img/funmageddon_pozadí.png');
    background-size: cover;
    background-attachment: fixed;
    background-position: center;
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', cursive;
    font-weight: 400;
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    margin-top: 0;
    margin-bottom: -5.5rem;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    color: var(--accent-yellow);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    margin-top: 0;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: var(--accent-orange);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Header and Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: var(--primary-bg); /* nebo jiná barva, ať se nepřekrývá průhledně */
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    gap: 1rem;
}

.nav-logo .logo {
    height: 170px;
    width: auto;
    transition: transform 0.3s ease;
}

.nav-logo .logo:hover {
    transform: scale(1.1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 1rem;
    align-items: center;
    flex-wrap: nowrap;
    white-space: nowrap;
}

.nav-link {
    position: relative;
    display: inline-block; /* nebo block, pokud chceš větší klikací plochu */
    color: var(--text-light);
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 0.8rem;
    border-radius: 25px;
    overflow: hidden;
    font-size: 1.15rem;
    z-index: 0;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    z-index: -1;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    border-radius: 25px; /* zarovná se s paddingem */
}

.nav-link:hover::before,
.nav-link.active::before {
    transform: translateX(0);
}

/* Social Icons */
.social-icons {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.social-link {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-link svg {
    width: 20px;
    height: 20px;
}

.social-link.youtube {
    background: #FF0000;
    color: white;
}

.social-link.tiktok {
    background: #000000;
    color: white;
}

.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%);
    color: white;
}

.social-link.facebook {
    background: #1877F2;
    color: white;
}

.social-link:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: var(--shadow-light);
}

/* Language Selector */
.language-selector {
    flex-shrink: 0;
    min-width: 120px;
}

.language-selector select {
    background: var(--secondary-bg);
    color: var(--text-light);
    border: 2px solid var(--accent-yellow);
    border-radius: 20px;
    padding: 0.5rem 1rem;
    font-family: inherit;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    min-width: 120px;
}

.language-selector select:hover {
    background: var(--accent-yellow);
    color: var(--text-dark);
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background: var(--accent-yellow);
    transition: 0.3s;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    padding-top: 250px; /* 150px navbar + 100px vzduchu */
}

/* Other page sections */
.about-hero, .contact-hero, .gallery-hero, .characters-hero {
    padding-top: 250px !important; /* 150px navbar + 100px vzduchu */
    padding-bottom: 2rem;
    min-height: calc(100vh - 250px);
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 35, 50, 0.7);
    z-index: 0;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.hero-title {
    margin-bottom: 1.5rem;
    animation: slideInLeft 1s ease-out 0.2s both;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2vw, 1.3rem);
    color: var(--text-gray);
    margin-bottom: 2.5rem;
    animation: slideInRight 1s ease-out 0.4s both;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-dark);
    box-shadow: var(--shadow-light);
}

.btn-secondary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid var(--accent-blue);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-dark);
}

.btn-primary:hover {
    background: var(--gradient-secondary);
    color: var(--text-light);
}

.btn-secondary:hover {
    background: var(--accent-blue);
    color: var(--text-light);
}

/* Latest Video Section */
.latest-video {
    padding: 4rem 0;
    background: rgba(36, 52, 71, 0.8);
    backdrop-filter: blur(10px);
}

.section-title {
    text-align: center;
    margin-bottom: 3rem;
}

.video-container {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-dark);
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Features Section */
.features {
    padding: 4rem 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: rgba(36, 52, 71, 0.9);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent-yellow);
    box-shadow: var(--shadow-light);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
}

.feature-card h3 {
    margin-bottom: 1rem;
}

.feature-card p {
    color: var(--text-gray);
    line-height: 1.6;
}

/* Footer */
footer {
    background: rgba(26, 35, 50, 0.95);
    backdrop-filter: blur(10px);
    padding: 3rem 0 1rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    align-items: center;
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 60px;
    width: auto;
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.footer-links a {
    color: var(--text-gray);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--accent-yellow);
}

.footer-social {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.footer-social .social-link {
    width: auto;
    height: auto;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--secondary-bg);
    color: var(--text-gray);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Medium screens - adjust navigation */
@media (max-width: 1024px) {
    .nav-menu {
        gap: 0.5rem;
    }
    
    .nav-link {
        padding: 0.4rem 0.6rem;
        font-size: 0.9rem;
    }
    
    .language-selector {
        min-width: 100px;
    }
    
    .language-selector select {
        min-width: 100px;
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 90px;
        flex-direction: column;
        background-color: rgba(26, 35, 50, 0.98);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        padding: 2rem 0;
        gap: 1rem;
        order: 4;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
        order: 3;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .social-icons {
        order: 1;
        flex-shrink: 0;
    }

    .language-selector {
        order: 2;
        flex-shrink: 0;
        min-width: 100px;
    }

    .language-selector select {
        min-width: 100px;
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .footer-links,
    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .nav-container {
        padding: 0 1rem;
    }

    .hero {
        padding: 6rem 1rem 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .feature-card {
        padding: 2rem;
    }
}

/* Page-specific styles */
.page-header {
    padding: 8rem 0 4rem;
    text-align: center;
    background: rgba(26, 35, 50, 0.8);
    backdrop-filter: blur(10px);
}

.page-content {
    padding: 4rem 0;
    max-width: 800px;
    margin: 0 auto;
}

.page-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--text-gray);
}

/* Character cards */
.characters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.character-card {
    background: rgba(36, 52, 71, 0.9);
    backdrop-filter: blur(10px);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.character-card:hover {
    transform: translateY(-10px);
    border-color: var(--accent-orange);
    box-shadow: var(--shadow-light);
}

.character-avatar {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    border: 4px solid var(--accent-yellow);
}

.character-name {
    color: var(--accent-yellow);
    margin-bottom: 1rem;
}

.character-description {
    color: var(--text-gray);
    line-height: 1.6;
}

/* Gallery styles */
.gallery-section {
    margin-bottom: 4rem;
    background: rgba(36, 52, 71, 0.8);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 20px;
}

.platform-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.platform-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.video-item h4 {
    color: var(--accent-yellow);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.video-embed {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow-dark);
}

.video-embed iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.platform-link {
    display: inline-block;
    background: var(--gradient-primary);
    color: var(--text-dark);
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.platform-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* Contact form styles */
.contact-info {
    background: rgba(36, 52, 71, 0.9);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.contact-email {
    color: var(--accent-yellow);
    font-size: 1.3rem;
    font-weight: 700;
    text-decoration: none;
    display: inline-block;
    margin: 1rem 0;
    padding: 1rem 2rem;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    border: 2px solid var(--accent-yellow);
    transition: all 0.3s ease;
}

.contact-email:hover {
    background: var(--accent-yellow);
    color: var(--text-dark);
    transform: translateY(-2px);
}
.spacer {
    height: 150px;
}
.email-copy-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    text-align: center;
    flex-wrap: wrap;
}

.copy-btn {
    background: var(--accent-yellow, #ffd600);
    border: none;
    padding: 0.4rem 0.6rem;
    cursor: pointer;
    font-size: 1.1rem;
    border-radius: 5px;
    transition: background 0.2s;
}

.copy-btn:hover {
    background: #ffce00;
}

.copy-message {
    margin-left: 0.5rem;
    font-size: 0.9rem;
    color: var(--accent-yellow);
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    font-weight: 600;
}

.email-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 1rem 0;
    flex-wrap: wrap;
}

.email {
    color: var(--accent-yellow);
    font-size: 1.2rem;
    font-weight: 600;
}

.contact-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.contact-intro {
    font-size: 1.1rem;
    margin-bottom: 3rem;
    color: var(--text-gray);
}

.contact-info {
    background: rgba(36, 52, 71, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid var(--accent-yellow);
    padding: 2rem;
    margin: 2rem auto;
    max-width: 600px;
}

.contact-label {
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.response-time {
    margin: 2rem 0 1rem 0;
    color: var(--accent-blue);
    font-size: 1rem;
}

.contact-closing {
    margin-top: 2rem;
    font-size: 1.1rem;
    color: var(--text-gray);
    font-style: italic;
}

/* About page styling */
.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.about-text {
    text-align: left;
    margin-top: 2rem;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}
}

/* Loading placeholders for video content */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    padding: 3rem 2rem;
    text-align: center;
    border-radius: 15px;
    color: #666;
}

.loading-placeholder p {
    margin: 0;
    font-weight: 600;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Video placeholder styles */
.tiktok-placeholder,
.instagram-placeholder,
.facebook-placeholder {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tiktok-placeholder:hover,
.instagram-placeholder:hover,
.facebook-placeholder:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.tiktok-placeholder a,
.instagram-placeholder a,
.facebook-placeholder a {
    transition: all 0.3s ease;
}

.tiktok-placeholder a:hover,
.instagram-placeholder a:hover,
.facebook-placeholder a:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Error message styles */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 2rem;
    text-align: center;
    border-radius: 15px;
    border: 1px solid #f5c6cb;
}

/* Responsive adjustments for video placeholders */
@media (max-width: 768px) {
    .loading-placeholder,
    .tiktok-placeholder,
    .instagram-placeholder,
    .facebook-placeholder {
        padding: 2rem 1rem;
    }
}

